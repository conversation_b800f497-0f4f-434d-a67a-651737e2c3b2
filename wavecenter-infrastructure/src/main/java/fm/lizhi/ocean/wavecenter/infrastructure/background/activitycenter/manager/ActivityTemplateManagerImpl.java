package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateGeneralPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateHotPageBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityTemplateInfoBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestCountGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestPageHotActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetGeneralActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.JoinerUtils;
import fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity.ActivityTemplateUsedRelation;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfo;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityTemplateUsedRelationDao;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityTemplateInfoConverter;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityResourceDao;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.dao.ActivityTemplateDao;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.CommonActivityConfig;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateGiftTaskManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.gift.manager.GiftManager;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.constants.PendantStatusEnum;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.*;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.manager.PendantConditionManager;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.manager.PendantManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.CreateUserGroupParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.CreateUserGroupResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.usergroup.UpdateUserGroupParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserGroupManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动模板管理器实现
 */
@Component
@Slf4j
public class ActivityTemplateManagerImpl implements ActivityTemplateManager {

    /**
     * 主播白名单用户组名称格式
     */
    private static final String WHITE_NJ_USER_GROUP_NAME_PATTERN = "【%s】活动厅名单";

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityResourceDao activityResourceDao;

    @Autowired
    private ActivityTemplateDao activityTemplateDao;
    @Autowired
    private ActivityTemplateInfoConverter activityTemplateInfoConverter;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private ActivityTemplateGiftTaskManager activityTemplateGiftTaskManager;

    @Autowired
    private PendantManager pendantManager;

    @Autowired
    private PendantConditionManager pendantConditionManager;

    @Autowired
    private ActivityTemplateUsedRelationDao activityTemplateUsedRelationDao;

    @Autowired
    private UserGroupManager userGroupManager;

    @Autowired
    private GiftManager giftManager;

    @Override
    public Result<Long> createTemplate(RequestCreateActivityTemplate req) {
        log.info("createTemplate req={}", req);
        Result<Void> validateFlowResources = validateFlowResources(req.getAppId(), req.getFlowResources());
        if (RpcResult.isFail(validateFlowResources)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, validateFlowResources.getMessage());
        }

        // 生成主播白名单用户组ID
        Result<Long> generateWhiteNjUserGroupIdResult = this.generateWhiteNjUserGroupId(req);
        if (RpcResult.isFail(generateWhiteNjUserGroupIdResult)) {
            return RpcResult.fail(generateWhiteNjUserGroupIdResult.rCode(), generateWhiteNjUserGroupIdResult.getMessage());
        }
        Long whiteNjUserGroupId = generateWhiteNjUserGroupIdResult.target();

        long templateId = activityTemplateDao.createTemplate(req, whiteNjUserGroupId);

        // 创建礼物自动上下架任务
        activityTemplateGiftTaskManager.createGiftTask(req.getGiftIds(), req.getAppId(),
                req.getActivityStartTimeLimit(), req.getActivityEndTimeLimit(), templateId
        );
        // 修改礼物关联的白名单用户组
        Result<Void> updateGiftsWhiteNjGroupResult = this.updateGiftsWhiteNjGroup(req, whiteNjUserGroupId);
        if (RpcResult.isFail(updateGiftsWhiteNjGroupResult)) {
            return RpcResult.fail(updateGiftsWhiteNjGroupResult.rCode(), updateGiftsWhiteNjGroupResult.getMessage());
        }
        // 添加礼物到礼物大类
        Long giftGroupId = activityConfig.getBizConfig(req.getAppId()).getGiftGroupId();
        Result<Void> addGiftsToGiftGroupResult = this.addGiftsToGiftGroup(req, giftGroupId);
        if (RpcResult.isFail(addGiftsToGiftGroupResult)) {
            return RpcResult.fail(addGiftsToGiftGroupResult.rCode(), addGiftsToGiftGroupResult.getMessage());
        }

        // 设置挂件有效期
        Boolean pendantRes = updatePendantShowTimeAndStatus(req.getFlowResources(), req.getActivityStartTimeLimit(), req.getActivityEndTimeLimit(), PendantStatusEnum.ONLINE);
        if (!pendantRes) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "设置挂件有效期失败");
        }
        // 修改挂件关联的白名单用户组
        saveOrUpdatePendantWhiteNjGroup(req.getFlowResources(), whiteNjUserGroupId);

        log.info("createTemplate templateId={}", templateId);
        return RpcResult.success(templateId);
    }




    private Result<Void> validateFlowResources(Integer appId, List<ActivityTemplateFlowResourceBean> flowResources) {
        if (CollectionUtils.isEmpty(flowResources)) {
            return RpcResult.success();
        }
        // 查询资源配置
        ArrayList<Long> resourceConfigIds = new ArrayList<>();
        for (ActivityTemplateFlowResourceBean flowResource : flowResources) {
            resourceConfigIds.add(flowResource.getResourceConfigId());
        }
        Map<Long, ActivityResourceConfig> resourceConfigIdToResourceConfigMap = activityResourceDao
                .getResourceByIdsAsMap(resourceConfigIds);
        CommonActivityConfig bizConfig = activityConfig.getBizConfig(appId);
        // 校验资源
        for (ActivityTemplateFlowResourceBean flowResource : flowResources) {
            Long resourceConfigId = flowResource.getResourceConfigId();
            ActivityTemplateFlowResourceExtraBean extra = flowResource.getExtra();
            ActivityResourceConfig resourceConfig = resourceConfigIdToResourceConfigMap.get(resourceConfigId);
            if (resourceConfig == null
                    || Objects.equals(resourceConfig.getDeleted(), LogicDeleteConstants.DELETED)
                    || Objects.equals(resourceConfig.getStatus(), ActivityResourceStatusEnum.DISABLED.getValue())) {
                log.info("validateFlowResources resourceConfig is invalid, resourceConfigId={}", resourceConfigId);
                return RpcResult.fail(CommonService.PARAM_ERROR, "无效的资源配置id, id=" + resourceConfigId);
            }
            // 校验官频位资源
            if (Objects.equals(resourceConfig.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())) {
                // 先保留，保证平滑上线
                Integer durationLimit = extra.getDurationLimit();
                List<Integer> officialSeatDurationLimits = bizConfig.getOfficialSeatDurationLimits();
                if (durationLimit != null && !officialSeatDurationLimits.contains(durationLimit)) {
                    log.info("validateFlowResources durationLimit is invalid, resourceConfigId={}, durationLimit: {}," +
                            " officialSeatDurationLimits: {}", resourceConfigId, durationLimit, officialSeatDurationLimits);
                    return RpcResult.fail(CommonService.PARAM_ERROR, "官频位资源时长限制无效");
                }
                List<Integer> officialSeatNumbers = extra.getOfficialSeatNumbers();
                if (CollectionUtils.isEmpty(officialSeatNumbers)) {
                    log.info("validateFlowResources officialSeatNumbers is empty, resourceConfigId={}", resourceConfigId);
                    return RpcResult.fail(CommonService.PARAM_ERROR, "官频位资源开放位置不能为空");
                }
                List<Integer> officialSeatAvailableNumbers = bizConfig.getOfficialSeatAvailableNumbers();
                if (!CollectionUtils.containsAll(officialSeatAvailableNumbers, officialSeatNumbers)) {
                    log.info("validateFlowResources officialSeatNumbers is invalid, resourceConfigId={}," +
                                    " officialSeatNumbers: {}, officialSeatAvailableNumbers: {}", resourceConfigId,
                            officialSeatNumbers, officialSeatAvailableNumbers);
                    return RpcResult.fail(CommonService.PARAM_ERROR, "官频位资源开放位置无效");
                }
            }else if (Objects.equals(resourceConfig.getResourceCode(), AutoConfigResourceEnum.PENDANT.getResourceCode())){
                if (extra.getPendantId() == null) {
                    log.info("validateFlowResources pendantId is empty, resourceConfigId={}", resourceConfigId);
                    return RpcResult.fail(CommonService.PARAM_ERROR, "挂件资源挂件ID不能为空");
                }
            }
        }
        return RpcResult.success();
    }

    /**
     * 生成主播白名单用户组ID. 仅当活动模板需要用到主播白名单用户组时才创建, 否则返回0. 目前是礼物和挂件需要
     *
     * @param req 请求
     * @return 结果, 包含用户组ID, 0表示该模板不需要创建白名单用户组ID
     */
    private Result<Long> generateWhiteNjUserGroupId(RequestSaveActivityTemplate req) {
        if (this.containsGift(req) || this.containsPendant(req)) {
            return this.createWhiteNjUserGroup(req);
        } else {
            return RpcResult.success(0L);
        }
    }

    private boolean containsGift(RequestSaveActivityTemplate req) {
        return CollectionUtils.isNotEmpty(req.getGiftIds());
    }

    private boolean containsPendant(RequestSaveActivityTemplate req) {
        for (ActivityTemplateFlowResourceBean flowResourceBean : ListUtils.emptyIfNull(req.getFlowResources())) {
            if (Objects.equals(flowResourceBean.getResourceCode(), AutoConfigResourceEnum.PENDANT.getResourceCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 创建主播白名单用户组
     *
     * @param req 请求
     * @return 结果, 包含用户组ID
     */
    private Result<Long> createWhiteNjUserGroup(RequestSaveActivityTemplate req) {
        String groupName = String.format(WHITE_NJ_USER_GROUP_NAME_PATTERN, req.getName());
        CreateUserGroupParamDTO param = new CreateUserGroupParamDTO();
        param.setName(groupName);
        Result<CreateUserGroupResultDTO> result = userGroupManager.createUserGroup(param);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        return RpcResult.success(result.target().getId());
    }

    /**
     * 修改礼物关联的白名单用户组
     *
     * @param req                请求
     * @param whiteNjUserGroupId 白名单用户组ID
     * @return 结果
     */
    private Result<Void> updateGiftsWhiteNjGroup(RequestSaveActivityTemplate req, long whiteNjUserGroupId) {
        for (Long giftId : ListUtils.emptyIfNull(req.getGiftIds())) {
            Result<Void> result = giftManager.updateWhiteNjGroupId(giftId, whiteNjUserGroupId);
            if (RpcResult.isFail(result)) {
                return result;
            }
        }
        return RpcResult.success();
    }

    /**
     * 添加礼物到礼物分类
     *
     * @param req         请求
     * @param giftGroupId 礼物分类ID
     * @return 结果
     */
    private Result<Void> addGiftsToGiftGroup(RequestSaveActivityTemplate req, long giftGroupId) {
        for (Long giftId : ListUtils.emptyIfNull(req.getGiftIds())) {
            Result<Void> result = giftManager.addGiftToGroup(giftId, giftGroupId);
            if (RpcResult.isFail(result)) {
                return result;
            }
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> updateTemplate(RequestUpdateActivityTemplate req) {
        log.info("updateTemplate req={}", req);
        Long templateId = req.getId();
        ActivityTemplateInfo oldTemplateInfo = activityTemplateDao.getTemplateInfoById(templateId);
        //查询流量资源
        List<ActivityTemplateFlowResourceDTO> oldFlowResources = activityTemplateDao.getFlowResourceDTOS(templateId);
        if (oldTemplateInfo == null) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的更新模板参数, id=" + templateId);
        }
        Result<Void> validateFlowResources = validateFlowResources(req.getAppId(), req.getFlowResources());
        if (RpcResult.isFail(validateFlowResources)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, validateFlowResources.getMessage());
        }

        // 调用创建白名单接口，拿到 whiteNjUserGroupId 并设置
        Long whiteNjUserGroupId = oldTemplateInfo.getWhiteNjUserGroupId();
        if (whiteNjUserGroupId == null || whiteNjUserGroupId <= 0) {
            Result<Long> whiteNjUserGroupIdResult = this.generateWhiteNjUserGroupId(req);
            if (RpcResult.isFail(whiteNjUserGroupIdResult)) {
                return RpcResult.fail(whiteNjUserGroupIdResult.rCode(), whiteNjUserGroupIdResult.getMessage());
            }
            whiteNjUserGroupId = whiteNjUserGroupIdResult.target();
        }
        activityTemplateDao.updateTemplate(req, whiteNjUserGroupId);

        // 修改白名单用户组名称
        String oldTemplateName = oldTemplateInfo.getName();
        String newTemplateName = req.getName();
        if (!Objects.equals(oldTemplateName, newTemplateName) && whiteNjUserGroupId != null && whiteNjUserGroupId > 0) {
            this.updateWhiteNjUserGroupName(whiteNjUserGroupId, newTemplateName);
        }
        // 修改礼物关联的白名单用户组
        Result<Void> updateGiftsWhiteNjGroupResult = this.updateGiftsWhiteNjGroup(req, whiteNjUserGroupId);
        if (RpcResult.isFail(updateGiftsWhiteNjGroupResult)) {
            return RpcResult.fail(updateGiftsWhiteNjGroupResult.rCode(), updateGiftsWhiteNjGroupResult.getMessage());
        }
        // 添加礼物到礼物大类
        Long giftGroupId = activityConfig.getBizConfig(req.getAppId()).getGiftGroupId();
        Result<Void> addGiftsToGiftGroupResult = this.addGiftsToGiftGroup(req, giftGroupId);
        if (RpcResult.isFail(addGiftsToGiftGroupResult)) {
            return RpcResult.fail(addGiftsToGiftGroupResult.rCode(), addGiftsToGiftGroupResult.getMessage());
        }

        //更新礼物自动上下架任务
        updateGiftTask(req, oldTemplateInfo);

        // 设置挂件有效期
        updatePendantShowTimeAndStatus(req.getFlowResources(), req.getActivityStartTimeLimit(), req.getActivityEndTimeLimit(), PendantStatusEnum.ONLINE);
        // 修改挂件关联的白名单用户组
        saveOrUpdatePendantWhiteNjGroup(req.getFlowResources(), whiteNjUserGroupId);
        // 失效旧有挂件
        invalidateOldPendant(oldFlowResources, req.getFlowResources());
        return RpcResult.success();
    }


    private void invalidateOldPendant(List<ActivityTemplateFlowResourceDTO> oldFlowResources, List<ActivityTemplateFlowResourceBean> newFlowResources) {

        if (CollectionUtils.isEmpty(oldFlowResources)) {
            return;
        }

        Set<Long> newPendentIdMap = newFlowResources.stream().filter(Objects::nonNull)
                .filter(flowResources -> flowResources.getExtra() != null && flowResources.getExtra().getPendantId() != null)
                .map(newResource -> newResource.getExtra().getPendantId()).collect(Collectors.toSet());

        //找到旧挂件
        List<Long> pendantIds = oldFlowResources.stream().filter(Objects::nonNull)
                .filter(flowResource -> flowResource.getExtra() != null && flowResource.getExtra().getPendantId() != null)
                .filter(oldFlowResource -> !newPendentIdMap.contains(oldFlowResource.getExtra().getPendantId()))
                .map(flowResource -> flowResource.getExtra().getPendantId()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pendantIds)) {
            return;
        }

        pendantIds.forEach(pendantId -> {
            PendantStatusParamDTO paramDTO = new PendantStatusParamDTO().setPendantId(pendantId).setStatus(PendantStatusEnum.OFFLINE);
            boolean result = pendantManager.updatePendantStatus(paramDTO);
            // 失败打印日志即可，不影响整体流程
            if (!result) {
                log.warn("invalidateOldPendant pendantId={} update pendant status error", pendantId);
            }
        });
    }

    private void updateWhiteNjUserGroupName(long whiteNjUserGroupId, String templateName) {
        // 更新用户组名称失败也不影响更新模板, 只记录日志
        try {
            String newGroupName = String.format(WHITE_NJ_USER_GROUP_NAME_PATTERN, templateName);
            UpdateUserGroupParamDTO param = new UpdateUserGroupParamDTO();
            param.setId(whiteNjUserGroupId);
            param.setName(newGroupName);
            userGroupManager.updateUserGroup(param);
        } catch (RuntimeException e) {
            log.info("updateWhiteNjUserGroupName failed, whiteNjUserGroupId={}, templateName={}", whiteNjUserGroupId, templateName, e);
        }
    }

    /**
     * 更新礼物自动上下架任务
     *
     * @param req            请求
     * @param oldTemplateInfo 旧的模板信息
     */
    private void updateGiftTask(RequestUpdateActivityTemplate req, ActivityTemplateInfo oldTemplateInfo) {
        Long templateId = oldTemplateInfo.getId();
        Integer appId = oldTemplateInfo.getAppId();
        List<Long> oldGiftIds = JoinerUtils.splitLong(oldTemplateInfo.getGiftIds()).stream().distinct().sorted().collect(Collectors.toList());
        List<Long> newGiftIds = ListUtils.emptyIfNull(req.getGiftIds()).stream().distinct().sorted().collect(Collectors.toList());
        Long oldStartTime = oldTemplateInfo.getActivityStartTimeLimit() != null ? oldTemplateInfo.getActivityStartTimeLimit().getTime() : null;
        Long oldEndTime = oldTemplateInfo.getActivityEndTimeLimit() != null ? oldTemplateInfo.getActivityEndTimeLimit().getTime() : null;
        Long newStartTime = req.getActivityStartTimeLimit();
        Long newEndTime = req.getActivityEndTimeLimit();

        // 如果开始时间或结束时间出现变化，则删除旧的任务，创建新的任务
        if (!Objects.equals(oldStartTime, newStartTime) || !Objects.equals(oldEndTime, newEndTime)) {
            activityTemplateGiftTaskManager.deleteGiftTask(appId, templateId);
            activityTemplateGiftTaskManager.createGiftTask(newGiftIds, appId, newStartTime, newEndTime, templateId);
            return;
        }

        // 如果开始时间和结束时间没变化，且礼物ID也都没有变化，则不做任何操作直接返回
        if (Objects.equals(oldGiftIds, newGiftIds)) {
            log.info("updateGiftTask no need to update gift task, templateId={}", req.getId());
            return;
        }

        // 如果开始时间和结束时间没变化，但礼物ID有变化，则计算新增和删除的礼物ID, 用于增量更新任务
        List<Long> deletedGiftIds = ListUtils.subtract(oldGiftIds, newGiftIds);
        List<Long> addedGiftIds = ListUtils.subtract(newGiftIds, oldGiftIds);
        activityTemplateGiftTaskManager.deleteGiftTaskByGiftIds(appId, templateId, deletedGiftIds);
        activityTemplateGiftTaskManager.createGiftTask(addedGiftIds, appId, newStartTime, newEndTime, templateId);
    }


    @Override
    public Result<Void> deleteTemplate(RequestDeleteActivityTemplate req) {
        log.info("deleteTemplate req={}", req);
        Long templateId = req.getId();
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的删除模板参数, id=" + templateId);
        }

        // 删除礼物自动上下架任务
        activityTemplateGiftTaskManager.deleteGiftTask(req.getAppId(), templateId);

        activityTemplateDao.deleteTemplate(req);
        return RpcResult.success();
    }

    @Override
    public Result<Void> updateShelfStatus(RequestUpdateActivityTemplateShelfStatus req) {
        log.info("updateShelfStatus req={}", req);
        Long templateId = req.getId();
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的更新模板上下架状态参数, id=" + templateId);
        }
        activityTemplateDao.updateShelfStatus(req);
        return RpcResult.success();
    }

    @Override
    public void updateStatus(Long templateId, ActivityTemplateStatusEnum status) {
        log.info("templateId={}, status={}", templateId, status);
        activityTemplateDao.updateStatus(templateId, status);
    }

    @Override
    public Result<ResponseGetActivityTemplateShelfStatus> getShelfStatus(long templateId) {
        log.info("getShelfStatus templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的获取模板上下架状态参数, id=" + templateId);
        }
        ResponseGetActivityTemplateShelfStatus resp = activityTemplateDao.getShelfStatus(templateId);
        log.debug("getShelfStatus resp={}", resp);
        return RpcResult.success(resp);
    }

    @Override
    public Result<PageBean<ActivityTemplatePageBean>> pageTemplate(RequestPageActivityTemplate req) {
        log.info("pageTemplate req={}", req);
        PageList<ActivityTemplatePageBean> page = activityTemplateDao.pageTemplate(req);
        return RpcResult.success(PageBean.of(page.getTotal(), page));
    }

    @Override
    public Result<ResponseGetActivityTemplate> getTemplate(long templateId) {
        log.info("getTemplate templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的获取模板参数, id=" + templateId);
        }
        ResponseGetActivityTemplate resp = activityTemplateDao.getTemplate(templateId);
        log.debug("getTemplate resp={}", resp);
        return RpcResult.success(resp);
    }

    @Override
    public Result<PageBean<ActivityTemplateHotPageBean>> pageHotTemplate(RequestPageHotActivityTemplate req, List<Long> njList) {
        log.info("pageHotTemplate req={}", req);
        PageList<ActivityTemplateHotPageBean> page = activityTemplateDao.pageHotTemplate(req, njList);
        return RpcResult.success(PageBean.of(page.getTotal(), page));
    }

    @Override
    public Result<PageBean<ActivityTemplateGeneralPageBean>> pageGeneralTemplate(RequestPageGeneralActivityTemplate req) {
        log.info("pageGeneralTemplate req={}", req);
        // 抽取为公共方法
        Optional<Long> njId = enrichRequestWithUserNjIds(req.getRequestUserId());
        njId.ifPresent(aLong -> req.getNjIds().add(aLong));

        PageList<ActivityTemplateGeneralPageBean> page = activityTemplateDao.pageGeneralTemplate(req);
        return RpcResult.success(PageBean.of(page.getTotal(), page));
    }

    @Override
    public Result<Long> countGeneralTemplate(RequestCountGeneralActivityTemplate req) {
        log.info("countGeneralTemplate req={}", req);
        // 复用用户签约厅主查询逻辑
        Optional<Long> njIds = enrichRequestWithUserNjIds(req.getRequestUserId());
        njIds.ifPresent(aLong -> req.getNjIds().add(aLong));

        Long count = activityTemplateDao.countGeneralTemplate(req);
        return RpcResult.success(count);
    }

    /**
     * 根据用户信息丰富请求中的厅主ID列表
     *
     * @return
     */
    private Optional<Long> enrichRequestWithUserNjIds(Long requestUserId) {
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(requestUserId);
        if (userInFamily.isRoom() || userInFamily.isPlayer()) {
            log.info("njId={}", userInFamily.getNjId());
            return Optional.ofNullable(userInFamily.getNjId());
        }
        return Optional.empty();
    }

    @Override
    public Result<ResponseGetGeneralActivityTemplate> getGeneralTemplate(long templateId) {
        log.info("getGeneralTemplate templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            return RpcResult.fail(CommonService.PARAM_ERROR, "无效的获取通用模板详情参数, id=" + templateId);
        }
        ResponseGetGeneralActivityTemplate resp = activityTemplateDao.getGeneralTemplate(templateId);
        log.debug("getGeneralTemplate resp={}", resp);
        return RpcResult.success(resp);
    }

    @Override
    public ActivityTemplateInfoBean getTemplateInfoBean(Long templateId) {
        if (templateId == null) {
            log.warn("getTemplateInfoBean invalid. templateId is null");
            return null;
        }
        ActivityTemplateInfo templateInfo = activityTemplateDao.getTemplateInfoById(templateId);
        if (templateInfo == null) {
            log.warn("getTemplateInfoBean failed, templateInfo is null. templateId={}", templateId);
            return null;
        }
        return activityTemplateInfoConverter.toActivityTemplateInfoBean(templateInfo);
    }

    @Override
    public ActivityTemplateInfoDTO getTemplateInfoDTO(Long templateId) {
        if (templateId == null) {
            log.warn("getTemplateInfoDTO invalid. templateId is null");
            return null;
        }
        ActivityTemplateInfo templateInfo = activityTemplateDao.getTemplateInfoById(templateId);
        if (templateInfo == null) {
            log.warn("getTemplateInfoDTO failed, templateInfo is null. templateId={}", templateId);
            return null;
        }
        return activityTemplateInfoConverter.toActivityTemplateInfoDTO(templateInfo);
    }

    @Override
    public List<ActivityTemplateInfoBean> getTemplateInfoBeanByTemplateIds(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            log.warn("getTemplateInfoBeanByTemplateIds invalid. templateIds is empty");
            return Collections.emptyList();
        }

        List<ActivityTemplateInfo> templateInfos = activityTemplateDao.getTemplateInfoByIds(templateIds);
        if (CollectionUtils.isEmpty(templateInfos)) {
            log.warn("getTemplateInfoBeanByTemplateIds failed, templateInfos is empty. templateIds={}", templateIds);
            return Collections.emptyList();
        }
        return activityTemplateInfoConverter.toActivityTemplateInfoBeans(templateInfos);
    }

    @Override
    public ActivityTemplateInfoBean getTemplateInfoBeanByActivityId(Long activityId) {
        if (activityId == null) {
            log.warn("getTemplateInfoBeanByActivityId invalid. activityId is null");
            return null;
        }
        ActivityTemplateUsedRelation relation = activityTemplateUsedRelationDao.getTemplateUsedRelationByActivityId(activityId);
        if (relation != null) {
            return getTemplateInfoBean(relation.getTemplateId());
        }
        return null;
    }

    @Override
    public List<ActivityTemplateInfoBean> getTemplateInfoBeanByActivityIds(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            log.warn("getTemplateInfoBeanByActivityIds invalid. activityIds is empty");
            return Collections.emptyList();
        }
        List<ActivityTemplateUsedRelation> relations = activityTemplateUsedRelationDao.getTemplateUsedRelationByActivityIds(activityIds);
        if (CollectionUtils.isEmpty(relations)) {
            log.warn("getTemplateInfoBeanByActivityIds failed, relations is empty. activityIds={}", activityIds);
            return Collections.emptyList();
        }
        return relations.stream().map(ActivityTemplateUsedRelation::getTemplateId).distinct()
                .map(this::getTemplateInfoBean).filter(Objects::nonNull).collect(Collectors.toList());

    }

    @Override
    public ActivityTemplateFlowResourceDTO getTemplateOfficialSeat(long templateId) {
        log.info("getTemplateOfficialSeat templateId={}", templateId);
        if (activityTemplateDao.isTemplateAbsent(templateId)) {
            log.warn("getTemplateOfficialSeat failed, template is absent. templateId={}", templateId);
            return null;
        }
        return activityTemplateDao.getTemplateOfficialSeat(templateId);
    }

    @Override
    public List<Long> getTemplateNjWhitelist(long templateId) {
        return activityTemplateDao.getTemplateNjWhitelist(templateId, ContextUtils.getBusinessEvnEnum().getAppId());
    }

    @Override
    public ActivityTemplateFlowResourceDTO getTemplateProgramme(long templateId) {
        log.info("getTemplateProgramme templateId={}", templateId);
        return activityTemplateDao.getTemplateProgramme(templateId);
    }


    /**
     * 设置挂件有效期及状态
     *
     * @param flowResources
     * @param activityStartTimeLimit
     * @param activityEndTimeLimit
     * @param status
     */
    private Boolean updatePendantShowTimeAndStatus(List<ActivityTemplateFlowResourceBean> flowResources, Long activityStartTimeLimit, Long activityEndTimeLimit, PendantStatusEnum status) {
        if (CollectionUtils.isEmpty(flowResources)) {
            log.info("updatePendantShowTimeAndStatus flowResources is empty");
            return true;
        }

        // 更新挂件状态及时间
        flowResources.stream().filter(Objects::nonNull).forEach(flowResource -> {
            if (flowResource.getExtra() != null && flowResource.getExtra().getPendantId() != null){
                pendantManager.updatePendantShowTimeAndStatus(new PendantShowTimeAndStatusParamDTO().setPendantId(flowResource.getExtra().getPendantId())
                        .setStartTime(activityStartTimeLimit)
                        .setEndTime(activityEndTimeLimit)
                        .setStatus(status));
            }
        });

        return true;
    }


    private boolean saveOrUpdatePendantWhiteNjGroup(List<ActivityTemplateFlowResourceBean> flowResources, Long whiteNjUserGroupId) {
        if (CollectionUtils.isEmpty(flowResources) || whiteNjUserGroupId == null) {
            log.info("saveOrUpdatePendantWhiteNjGroup flowResources is empty");
            return true;
        }
        List<Long> pendantIds = flowResources.stream()
                .filter(flowResource -> flowResource.getExtra() != null)
                .filter(flowResource -> flowResource.getExtra().getPendantId() != null)
                .map(flowResource -> flowResource.getExtra().getPendantId())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pendantIds)) {
            return true;
        }

        pendantIds.stream().findFirst().ifPresent(pendantId -> {
            PendantDto pendantDto = pendantManager.getPendantWithConditions(pendantId);
            if (pendantDto == null){
                log.warn("saveOrUpdatePendantWhiteNjGroup pendantId={} get pendant is null", pendantId);
                return;
            }
            List<PendantConditionDTO> conditionList = pendantDto.getConditions()
                    .stream()
                    .map(dto -> {
                        if (whiteNjUserGroupId.equals(dto.getNjGroupId())) {
                            // 白名单一致，代表是创作者平台新增的条件
                            return dto;
                        }else {
                            // 判断条件 status 未失效，才删除
                            if (dto.getStatus() == PendantStatusEnum.ONLINE.getStatus()) {
                                // 不一致，失效该条件
                                boolean deletedCondition = pendantConditionManager.deleteCondition(pendantId, dto.getId());
                                if (!deletedCondition) {
                                    log.error("saveOrUpdatePendantWhiteNjGroup pendantId={} delete condition error, conditionId={}, success={}",
                                            pendantId, dto.getId(), deletedCondition);
                                }
                            }
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());


            if (CollectionUtils.isEmpty(conditionList)) {
                log.info("saveOrUpdatePendantWhiteNjGroup pendantId={} add condition", pendantId);
                pendantConditionManager.addCondition(new ConditionAddParamDTO()
                        .setPendantId(pendantId)
                        .setNjGroupId(whiteNjUserGroupId)
                );
            }else {
                conditionList.forEach(dto -> {
                    log.info("saveOrUpdatePendantWhiteNjGroup pendantId={} update condition, conditionId={}", pendantId, dto.getId());
                    pendantConditionManager.updateCondition(new ConditionUpdateParamDTO()
                            .setPendantId(pendantId)
                            .setConditionId(dto.getId())
                            .setNjGroupId(whiteNjUserGroupId)
                    );
                });
            }

        });
    }
}
